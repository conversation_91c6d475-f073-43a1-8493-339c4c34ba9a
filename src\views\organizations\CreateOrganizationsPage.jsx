import {
  AutocompleteItem,
  Avatar,
  Button,
  Radio,
  addToast,
} from "@heroui/react";
import { Add, Edit, Trash } from "iconsax-reactjs";
import { parseAsString, useQueryState } from "nuqs";
import { useEffect, useMemo, useState } from "react";
import { Form, useForm } from "react-hook-form";
import api from "../../api";
import FormAutoComplete from "../../components/form/FormAutoComplete";
import FormDatePicker from "../../components/form/FormDatePicker";
import FormInput from "../../components/form/FormInput";
import FormRadioGroup from "../../components/form/FormRadioGroup";
import FormRichText from "../../components/form/FormRichText/FormRichText";
import FormSwitch from "../../components/form/FormSwitch";
import FormUpload from "../../components/form/FormUpload";
import FormUploadAvatar from "../../components/form/FormUploadAvatar";
import Icon from "../../components/icon/Icon";
import PageWrapper from "../../components/layout/PageWrapper";

const CreateOrganizationsPage = () => {
  const { control, watch, setValue, handleSubmit } = useForm({
    defaultValues: {
      username: "", // For the autocomplete input
    },
  });

  const [selectedUsers, setSelectedUsers] = useState([]);
  const [username, setUsername] = useQueryState("username", parseAsString);

  // Get Entities Data
  const {
    data: _data,
    isLoading,
    error,
  } = api.Entities.list.useQuery({
    enabled: !!username,
    variables: {
      query: {
        username,
      },
    },
  });
  const users = useMemo(() => _data?.data, [_data]);
  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  console.log(users, selectedUsers);

  const onSubmit = (data) => {
    console.log(data);
  };

  return (
    <PageWrapper hasTitle={false}>
      <Form
        className="flex flex-col gap-4"
        control={control}
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex flex-col gap-6  rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <FormUploadAvatar
            name="image"
            control={control}
            classNames={{
              wrapper: "max-h-28 self-center md:self-auto max-w-28",
            }}
          />

          <p className="font-medium mt-2">اطلاعات سازمان</p>

          <div className="flex items-center flex-wrap md:flex-nowrap gap-4">
            <FormInput
              control={control}
              name="organizationName"
              type="text"
              inputProps={{
                classNames: {
                  base: "max-w-sm",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
                size: "lg",
                radius: "full",
                placeholder: "نام سازمان",
                startContent: <Edit className="size-6 text-primary" />,
              }}
            />
            <FormDatePicker
              control={control}
              name="date"
              isRange={true}
              size="lg"
              radius="full"
              startContent={
                <Icon className={"text-primary size-7"} name={"accept-note"} />
              }
              classNames={{
                base: "max-w-sm",
                inputWrapper:
                  "shadow-sm hover:!bg-background-100 border hover:border-foreground-200 transition-colors border-foreground-100",
                input: "text-sm ",
              }}
            />
          </div>

          <p className="font-medium mt-2">ویدیوی مرتبط با سازمان</p>

          <FormUpload control={control} name="video" />

          <p className="font-medium mt-2">توضیحات مرتبط با سازمان</p>
          <FormRichText
            control={control}
            name="description"
            enabledButtons={{
              image: false,
              link: false,
            }}
          />

          <FormSwitch
            control={control}
            className="ltr"
            name="active"
            label="وضعیت سازمان"
          />
        </div>

        <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <div className="flex w-full md:flex-nowrap flex-wrap items-center gap-4">
            <FormAutoComplete
              control={control}
              name="username"
              items={users?.data || []}
              isFilterable={true}
              renderItem={(item) => (
                <AutocompleteItem
                  classNames={{
                    selectedIcon: "hidden",
                    base: "hover:!bg-foreground-100/80 data-[hover=true]:bg-foreground-100/80",
                  }}
                  key={item?.id}
                  textValue={item?.fullname}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex gap-2 items-center">
                      <Avatar
                        alt={item?.fullname}
                        className="flex-shrink-0"
                        src={item?.avatar}
                      />
                      <div className="flex flex-col">
                        <span className="text-small ">{item?.fullname}</span>
                        <span className="text-tiny text-default-400">
                          {item?.mobile}
                        </span>
                      </div>
                    </div>
                    <Button
                      radius="full"
                      size="sm"
                      color="primary"
                      onPress={() => {
                        if (!selectedUsers.some((u) => u.id === item.id)) {
                          setSelectedUsers((prev) => [...prev, item]);
                        }
                      }}
                    >
                      افزودن
                    </Button>
                  </div>
                </AutocompleteItem>
              )}
              autoCompleteProps={{
                fullWidth: true,
                isClearable: false,
                multiple: false,
                listboxProps: {
                  emptyContent: "چیزی پیدا نشد",
                },
                isLoading: isLoading,
                inputProps: {
                  classNames: {
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                    input: "text-sm",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "جستجوی ادمین با استفاده از شماره موبایل",
                },
              }}
            />
            <Button
              type="button"
              color="primary"
              radius="full"
              className="px-6 shrink-0"
              startContent={<Add className="size-6" />}
            >
              ایجاد ادمین جدید
            </Button>
          </div>

          <ul className="space-y-4">
            {selectedUsers?.map((user) => (
              <li
                key={user.id}
                className="flex items-center rounded-3xl gap-4 p-6 border border-foreground-100 shadow-sm"
              >
                <Avatar
                  alt={user.fullname}
                  className="flex-shrink-0"
                  src={user.avatar}
                  size="lg"
                />

                <div className="flex items-center font-medium gap-2">
                  <p className="text-foreground-400">نام و نام خانوادگی: </p>
                  <p>{user.fullname}</p>
                </div>

                <div className="flex items-center font-medium gap-2">
                  <p className="text-foreground-400">شماره تماس: </p>
                  <p>{user.mobile}</p>
                </div>

                <Button
                  className="ms-auto"
                  isIconOnly
                  radius="full"
                  color="danger"
                  variant="light"
                  onPress={() => {
                    setSelectedUsers((prev) =>
                      prev.filter((u) => u.id !== user.id),
                    );
                  }}
                >
                  <Trash />
                </Button>
              </li>
            ))}
          </ul>

          <p className="font-medium mt-2">نقش</p>

          <div className="flex items-end md:flex-nowrap flex-wrap gap-3">
            <FormInput
              control={control}
              name="rolePersianName"
              inputProps={{
                placeholder: "عنوان نقش",
                // labelPlacement: "outside-left",
                radius: "full",
                size: "lg",
                classNames: {
                  base: "md:max-w-xs",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
              }}
            />
            <FormInput
              control={control}
              name="roleLatinName"
              inputProps={{
                placeholder: "نام لاتین",
                // labelPlacement: "outside-left",
                radius: "full",
                size: "lg",
                classNames: {
                  base: "md:max-w-xs",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
              }}
            />
          </div>

          <p className="font-medium mt-2">کاربران</p>

          <FormRadioGroup
            control={control}
            name="roleUsers"
            radioGroupProps={{
              orientation: "horizontal",
              classNames: {
                wrapper: "space-x-3 space-x-reverse",
              },
            }}
            items={[
              {
                key: "all",
                value: "all",
                label: "انتخاب همه",
              },
              {
                key: "viewers",
                value: "viewers",
                label: "مشاهده کاربران",
              },
              {
                key: "single_user",
                value: "single_user",
                label: "مشاهده عملکرد یک کاربر",
              },
            ]}
            renderItem={(item) => {
              return (
                <Radio
                  value={item.value}
                  key={item.key}
                  size="lg"
                  color="primary"
                  classNames={{
                    label: "text-sm font-medium text-inherit",
                    base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                  }}
                >
                  {item.label || item.value}
                </Radio>
              );
            }}
          />
          <p className="font-medium mt-2">پشتیبانی</p>

          <FormRadioGroup
            control={control}
            name="roleSupport"
            radioGroupProps={{
              orientation: "horizontal",
              classNames: {
                wrapper: "space-x-3 space-x-reverse",
              },
            }}
            items={[
              {
                key: "all",
                value: "all",
                label: "انتخاب همه",
              },
              {
                key: "viewers",
                value: "viewers",
                label: "مشاهده کاربران",
              },
              {
                key: "single_user",
                value: "single_user",
                label: "مشاهده عملکرد یک کاربر",
              },
            ]}
            renderItem={(item) => {
              return (
                <Radio
                  value={item.value}
                  key={item.key}
                  size="lg"
                  color="primary"
                  classNames={{
                    label: "text-sm font-medium text-inherit",
                    base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                  }}
                >
                  {item.label || item.value}
                </Radio>
              );
            }}
          />
          <p className="font-medium mt-2">سازمان</p>

          <FormRadioGroup
            control={control}
            name="roleOrganization"
            radioGroupProps={{
              orientation: "horizontal",
              classNames: {
                wrapper: "space-x-3 space-x-reverse",
              },
            }}
            items={[
              {
                key: "all",
                value: "all",
                label: "انتخاب همه",
              },
              {
                key: "viewers",
                value: "viewers",
                label: "مشاهده کاربران",
              },
              {
                key: "single_user",
                value: "single_user",
                label: "مشاهده عملکرد یک کاربر",
              },
            ]}
            renderItem={(item) => {
              return (
                <Radio
                  value={item.value}
                  key={item.key}
                  size="lg"
                  color="primary"
                  classNames={{
                    label: "text-sm font-medium text-inherit",
                    base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                  }}
                >
                  {item.label || item.value}
                </Radio>
              );
            }}
          />
          <div className="flex gap-3 justify-end items-center">
            <Button
              type="submit"
              radius="full"
              className="md:max-w-52"
              fullWidth
              size="lg"
            >
              انصراف
            </Button>
            <Button
              type="submit"
              color="primary"
              radius="full"
              className="md:max-w-52"
              fullWidth
              size="lg"
            >
              ثبت سازمان
            </Button>
          </div>
        </div>
      </Form>
    </PageWrapper>
  );
};

export default CreateOrganizationsPage;
